package com.cleevio.fundedmind.application.module.user.appuser.finder

import com.cleevio.fundedmind.application.common.finder.BaseFinderService
import com.cleevio.fundedmind.application.common.type.FirebaseId
import com.cleevio.fundedmind.application.common.type.HubspotId
import com.cleevio.fundedmind.application.common.type.StripeCustomerId
import com.cleevio.fundedmind.application.module.user.appuser.exception.UserNotFoundException
import com.cleevio.fundedmind.domain.user.appuser.AppUser
import com.cleevio.fundedmind.domain.user.appuser.AppUserRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
@Transactional(readOnly = true)
class AppUserFinderService(
    private val appUserRepository: AppUserRepository,
) : BaseFinderService<AppUser>(appUserRepository) {

    override fun errorBlock(message: String) = throw UserNotFoundException(message)

    override fun getEntityType() = AppUser::class

    fun getByEmail(email: String): AppUser = appUserRepository.findByEmailIgnoreCase(email)
        ?: errorBlock("User with email '$email' not found.")

    fun existByEmail(email: String): Boolean = appUserRepository.existsByEmailIgnoreCase(email)

    fun existByFirebaseIdentifier(firebaseIdentifier: FirebaseId): Boolean =
        appUserRepository.existsByFirebaseIdentifier(firebaseIdentifier)

    fun findByFirebaseIdentifier(firebaseIdentifier: FirebaseId): AppUser? =
        appUserRepository.findByFirebaseIdentifier(firebaseIdentifier)

    fun getByFirebaseIdentifier(firebaseIdentifier: FirebaseId): AppUser =
        appUserRepository.findByFirebaseIdentifier(firebaseIdentifier)
            ?: errorBlock("User with firebase identifier '$firebaseIdentifier' not found.")

    fun findByHubspotIdentifier(hubspotIdentifier: HubspotId): AppUser? =
        appUserRepository.findByHubspotIdentifier(hubspotIdentifier)

    fun getByHubspotIdentifier(hubspotIdentifier: HubspotId): AppUser =
        appUserRepository.findByHubspotIdentifier(hubspotIdentifier)
            ?: errorBlock("User with hubspot identifier '$hubspotIdentifier' not found.")

    fun findByEmail(email: String): AppUser? = appUserRepository.findByEmailIgnoreCase(email)

    fun getByStripeIdentifier(stripeCustomerId: StripeCustomerId): AppUser =
        appUserRepository.findByContainingStripeIdentifier(stripeCustomerId)
            ?: errorBlock("User with stripe identifier '$stripeCustomerId' not found.")

    fun findByStripeIdentifier(stripeCustomerId: StripeCustomerId): AppUser? =
        appUserRepository.findByContainingStripeIdentifier(stripeCustomerId)
}
