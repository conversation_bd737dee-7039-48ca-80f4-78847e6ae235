package com.cleevio.fundedmind.application.module.user.onboarding.event.listener

import com.cleevio.fundedmind.application.module.crm.port.out.CrmUserTierUpdatePort
import com.cleevio.fundedmind.application.module.crm.port.out.CrmUserUpdatePort
import com.cleevio.fundedmind.application.module.crm.port.out.UpdateCrmCustomerRequest
import com.cleevio.fundedmind.application.module.location.finder.UserLocationFinderService
import com.cleevio.fundedmind.application.module.user.appuser.finder.AppUserFinderService
import com.cleevio.fundedmind.application.module.user.onboarding.event.OnboardingFinishedEvent
import com.cleevio.fundedmind.application.module.user.onboarding.event.OnboardingTierUpdatedEvent
import com.cleevio.fundedmind.application.module.user.student.finder.OnboardingFinderService
import com.cleevio.fundedmind.application.module.user.student.finder.StudentFinderService
import io.sentry.spring.jakarta.tracing.SentryTransaction
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.transaction.event.TransactionPhase
import org.springframework.transaction.event.TransactionalEventListener

@Component
class OnboardingCrmEventListener(
    private val crmUserUpdatePort: CrmUserUpdatePort,
    private val crmUserTierUpdatePort: CrmUserTierUpdatePort,
    private val studentFinderService: StudentFinderService,
    private val appUserFinderService: AppUserFinderService,
    private val onboardingFinderService: OnboardingFinderService,
    private val userLocationFinderService: UserLocationFinderService,
) {

    @SentryTransaction(operation = "async.crm.onboarding-finished")
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun handleOnboardingFinishedEvent(event: OnboardingFinishedEvent) {
        val student = studentFinderService.getById(event.studentId)
        val appUser = appUserFinderService.getById(event.studentId)

        val realLocation = student.locationId?.let { locationId ->
            userLocationFinderService.getById(locationId).formattedAddress
        }

        crmUserUpdatePort.updateCrmUser(
            UpdateCrmCustomerRequest(
                firstName = student.firstName,
                lastName = student.lastName,
                phone = student.phone,
                countryCode = student.country,
                biography = student.biography,
                questionnaire = student.questionnaire,
                tier = student.studentTier,
                hubspotIdentifier = appUser.hubspotIdentifier,
                realLocation = realLocation,
            ),
        )
        crmUserUpdatePort.updateCrmUserQuestionnaire(
            hubspotIdentifier = appUser.hubspotIdentifier,
            questionnaire = student.questionnaire,
        )
    }

    @SentryTransaction(operation = "async.crm.onboarding-tier-updated")
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun handleOnboardingTierUpdatedEvent(event: OnboardingTierUpdatedEvent) {
        val appUser = appUserFinderService.getById(event.onboardingId)
        val onboarding = onboardingFinderService.getById(event.onboardingId)

        crmUserTierUpdatePort.updateCrmTier(
            onboardingTier = onboarding.onboardingTier,
            hubspotIdentifier = appUser.hubspotIdentifier,
        )
    }
}
