package com.cleevio.fundedmind.application.module.user.student

import com.cleevio.fundedmind.application.common.command.CommandHandler
import com.cleevio.fundedmind.application.common.constants.Locks
import com.cleevio.fundedmind.application.module.user.student.command.StudentPatchesPrivacyCommand
import com.cleevio.fundedmind.application.module.user.student.event.StudentPrivacySettingsUpdatedEvent
import com.cleevio.fundedmind.application.module.user.student.finder.StudentFinderService
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class StudentPatchesPrivacyCommandHandler(
    private val studentFinderService: StudentFinderService,
    private val applicationEventPublisher: ApplicationEventPublisher,
) : CommandHandler<Unit, StudentPatchesPrivacyCommand> {
    override val command = StudentPatchesPrivacyCommand::class

    @Transactional
    @Lock(module = Locks.Student.MODULE, lockName = Locks.Student.UPDATE)
    override fun handle(@LockFieldParameter("studentId") command: StudentPatchesPrivacyCommand) {
        studentFinderService
            .getById(command.studentId)
            .apply {
                patchPrivacy(
                    networkingVisibility = command.networkingVisibility,
                    levelVisibility = command.levelVisibility,
                )
            }
            .also {
                applicationEventPublisher.publishEvent(
                    StudentPrivacySettingsUpdatedEvent(studentId = it.id),
                )
            }
    }
}
