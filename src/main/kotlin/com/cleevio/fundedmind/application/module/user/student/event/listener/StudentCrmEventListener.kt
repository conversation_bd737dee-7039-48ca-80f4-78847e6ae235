package com.cleevio.fundedmind.application.module.user.student.event.listener

import com.cleevio.fundedmind.application.module.crm.port.out.CrmUserDiscordUpdatePort
import com.cleevio.fundedmind.application.module.crm.port.out.CrmUserLocationUpdatePort
import com.cleevio.fundedmind.application.module.crm.port.out.CrmUserPremiumDiscordAccessUpdatePort
import com.cleevio.fundedmind.application.module.crm.port.out.CrmUserPrivacyUpdatePort
import com.cleevio.fundedmind.application.module.crm.port.out.CrmUserTierUpdatePort
import com.cleevio.fundedmind.application.module.crm.port.out.CrmUserUpdatePort
import com.cleevio.fundedmind.application.module.crm.port.out.UpdateCrmCustomerRequest
import com.cleevio.fundedmind.application.module.location.finder.UserLocationFinderService
import com.cleevio.fundedmind.application.module.user.appuser.finder.AppUserFinderService
import com.cleevio.fundedmind.application.module.user.student.event.StudentConnectedDiscordEvent
import com.cleevio.fundedmind.application.module.user.student.event.StudentDisconnectedDiscordEvent
import com.cleevio.fundedmind.application.module.user.student.event.StudentDiscordSubscriptionActivatedEvent
import com.cleevio.fundedmind.application.module.user.student.event.StudentDiscordSubscriptionEndedEvent
import com.cleevio.fundedmind.application.module.user.student.event.StudentDiscordSubscriptionUpdatedEvent
import com.cleevio.fundedmind.application.module.user.student.event.StudentLocationUpdatedEvent
import com.cleevio.fundedmind.application.module.user.student.event.StudentPrivacySettingsUpdatedEvent
import com.cleevio.fundedmind.application.module.user.student.event.StudentProfileUpdatedEvent
import com.cleevio.fundedmind.application.module.user.student.event.StudentQuestionnaireUpdatedEvent
import com.cleevio.fundedmind.application.module.user.student.event.StudentTierUpdatedInAppEvent
import com.cleevio.fundedmind.application.module.user.student.finder.StudentDiscordFinderService
import com.cleevio.fundedmind.application.module.user.student.finder.StudentFinderService
import com.cleevio.fundedmind.application.module.user.student.service.UpdateStudentPremiumDiscordRoleService
import io.sentry.spring.jakarta.tracing.SentryTransaction
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.transaction.event.TransactionPhase
import org.springframework.transaction.event.TransactionalEventListener

@Component
class StudentCrmEventListener(
    private val appUserFinderService: AppUserFinderService,
    private val crmUserUpdatePort: CrmUserUpdatePort,
    private val crmUserTierUpdatePort: CrmUserTierUpdatePort,
    private val crmUserPremiumDiscordAccessUpdatePort: CrmUserPremiumDiscordAccessUpdatePort,
    private val crmUserDiscordUpdatePort: CrmUserDiscordUpdatePort,
    private val crmUserPrivacyUpdatePort: CrmUserPrivacyUpdatePort,
    private val crmUserLocationUpdatePort: CrmUserLocationUpdatePort,
    private val studentFinderService: StudentFinderService,
    private val studentDiscordFinderService: StudentDiscordFinderService,
    private val updateStudentPremiumDiscordRoleService: UpdateStudentPremiumDiscordRoleService,
    private val userLocationFinderService: UserLocationFinderService,
) {

    @SentryTransaction(operation = "async.crm.student-profile-updated")
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun handleStudentProfileUpdatedEvent(event: StudentProfileUpdatedEvent) {
        val student = studentFinderService.getById(event.studentId)
        val appUser = appUserFinderService.getById(event.studentId)

        val realLocation = student.locationId?.let { locationId ->
            userLocationFinderService.getById(locationId).formattedAddress
        }

        crmUserUpdatePort.updateCrmUser(
            UpdateCrmCustomerRequest(
                firstName = student.firstName,
                lastName = student.lastName,
                phone = student.phone,
                countryCode = student.country,
                biography = student.biography,
                questionnaire = student.questionnaire,
                tier = student.studentTier,
                hubspotIdentifier = appUser.hubspotIdentifier,
                realLocation = realLocation,
            ),
        )
    }

    @SentryTransaction(operation = "async.crm.student-tier-updated-in-app")
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun handleStudentTierUpdatedInAppEvent(event: StudentTierUpdatedInAppEvent) {
        val appUser = appUserFinderService.getById(event.studentId)
        val student = studentFinderService.getById(event.studentId)

        crmUserTierUpdatePort.updateCrmTier(
            studentTier = student.studentTier,
            hubspotIdentifier = appUser.hubspotIdentifier,
        )
    }

    @SentryTransaction(operation = "async.crm.student-discord-subscription-activated")
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun handleStudentDiscordSubscriptionActivatedEvent(event: StudentDiscordSubscriptionActivatedEvent) {
        val appUser = appUserFinderService.getById(event.studentId)
        val student = studentFinderService.getById(event.studentId)

        crmUserPremiumDiscordAccessUpdatePort.updateCrmUserPremiumDiscordAccess(
            hubspotIdentifier = appUser.hubspotIdentifier,
            premiumDiscordAccessEnds = requireNotNull(student.discordSubscriptionExpiresAt),
        )

        updateStudentPremiumDiscordRoleService.grantPremiumRole(student.id)
    }

    @SentryTransaction(operation = "async.crm.student-discord-subscription-updated")
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun handleStudentDiscordSubscriptionUpdatedEvent(event: StudentDiscordSubscriptionUpdatedEvent) {
        val appUser = appUserFinderService.getById(event.studentId)
        val student = studentFinderService.getById(event.studentId)

        crmUserPremiumDiscordAccessUpdatePort.updateCrmUserPremiumDiscordAccess(
            hubspotIdentifier = appUser.hubspotIdentifier,
            premiumDiscordAccessEnds = requireNotNull(student.discordSubscriptionExpiresAt),
        )
    }

    @SentryTransaction(operation = "async.crm.student-discord-subscription-ended")
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun handleStudentDiscordSubscriptionEndedEvent(event: StudentDiscordSubscriptionEndedEvent) {
        updateStudentPremiumDiscordRoleService.revokePremiumRole(studentId = event.studentId)
    }

    @SentryTransaction(operation = "async.crm.questionnaire-updated")
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun handleStudentQuestionnaireUpdatedEvent(event: StudentQuestionnaireUpdatedEvent) {
        val student = studentFinderService.getById(event.studentId)
        val appUser = appUserFinderService.getById(event.studentId)

        crmUserUpdatePort.updateCrmUserQuestionnaire(
            hubspotIdentifier = appUser.hubspotIdentifier,
            questionnaire = student.questionnaire,
        )
    }

    @SentryTransaction(operation = "async.crm.student-connected-discord")
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun handleStudentConnectedDiscordEvent(event: StudentConnectedDiscordEvent) {
        val studentDiscord = studentDiscordFinderService.getById(event.studentDiscordId)

        val appUser = appUserFinderService.getById(studentDiscord.studentId)

        crmUserDiscordUpdatePort.updateCrmUserDiscordNicknames(
            hubspotIdentifier = appUser.hubspotIdentifier,
            discordUserName = studentDiscord.userName,
            discordGlobalName = studentDiscord.globalName,
        )
    }

    @SentryTransaction(operation = "async.crm.student-disconnected-discord")
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun handleStudentDisconnectedDiscordEvent(event: StudentDisconnectedDiscordEvent) {
        val studentDiscord = studentDiscordFinderService.getByIdDeleted(event.studentDiscordId)

        val appUser = appUserFinderService.getById(studentDiscord.studentId)

        crmUserDiscordUpdatePort.updateCrmUserDiscordNicknames(
            hubspotIdentifier = appUser.hubspotIdentifier,
            discordUserName = studentDiscord.userName?.let { "$it [Disconnected]" },
            discordGlobalName = studentDiscord.globalName?.let { "$it [Disconnected]" },
        )
    }

    @SentryTransaction(operation = "async.crm.student-privacy-settings-updated")
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun handleStudentPrivacySettingsUpdatedEvent(event: StudentPrivacySettingsUpdatedEvent) {
        val student = studentFinderService.getById(event.studentId)
        val appUser = appUserFinderService.getById(event.studentId)

        crmUserPrivacyUpdatePort.updateCrmUserPrivacySettings(
            hubspotIdentifier = appUser.hubspotIdentifier,
            networkingVisibility = student.networkingVisibility,
            levelVisibility = student.levelVisibility,
        )
    }

    @SentryTransaction(operation = "async.crm.student-location-updated")
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun handleStudentLocationUpdatedEvent(event: StudentLocationUpdatedEvent) {
        val student = studentFinderService.getById(event.studentId)
        val appUser = appUserFinderService.getById(event.studentId)

        val realLocation = student.locationId?.let { locationId ->
            userLocationFinderService.getById(locationId).formattedAddress
        }

        crmUserLocationUpdatePort.updateCrmUserLocation(
            hubspotIdentifier = appUser.hubspotIdentifier,
            realLocation = realLocation,
        )
    }
}
