package com.cleevio.fundedmind.application.module.user.student

import com.cleevio.fundedmind.application.common.command.CommandHandler
import com.cleevio.fundedmind.application.common.constants.Locks
import com.cleevio.fundedmind.application.module.location.service.ProcessNewLocationService
import com.cleevio.fundedmind.application.module.user.student.command.StudentPatchesLocationCommand
import com.cleevio.fundedmind.application.module.user.student.event.StudentLocationUpdatedEvent
import com.cleevio.fundedmind.application.module.user.student.finder.StudentFinderService
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class StudentPatchesLocationCommandHandler(
    private val studentFinderService: StudentFinderService,
    private val processNewLocationService: ProcessNewLocationService,
    private val applicationEventPublisher: ApplicationEventPublisher,
) : CommandHandler<Unit, StudentPatchesLocationCommand> {
    override val command = StudentPatchesLocationCommand::class

    @Transactional
    @Lock(module = Locks.Student.MODULE, lockName = Locks.Student.UPDATE)
    override fun handle(@LockFieldParameter("studentId") command: StudentPatchesLocationCommand) {
        studentFinderService
            .getById(command.studentId)
            .apply {
                patchLocation(
                    processNewLocationService.processNewLocation(
                        currentLocationId = this.locationId,
                        newLocationInput = command.location,
                    ),
                )
            }
            .also {
                applicationEventPublisher.publishEvent(
                    StudentLocationUpdatedEvent(studentId = it.id),
                )
            }
    }
}
