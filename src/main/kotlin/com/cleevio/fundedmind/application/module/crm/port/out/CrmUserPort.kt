package com.cleevio.fundedmind.application.module.crm.port.out

import com.cleevio.fundedmind.application.common.type.HubspotId
import com.cleevio.fundedmind.domain.common.constant.Country
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.domain.common.constant.LevelVisibility
import com.cleevio.fundedmind.domain.common.constant.NetworkingVisibility
import com.cleevio.fundedmind.domain.common.constant.OnboardingTier
import com.cleevio.fundedmind.domain.common.constant.PaymentTierState
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.user.Questionnaire
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import java.time.Instant

interface CrmUserRoleUpdatePort {
    fun updateCrmUserRole(
        hubspotIdentifier: Long,
        role: UserRole,
    )
}

interface CrmUserTierUpdatePort {
    fun updateCrmTier(
        hubspotIdentifier: Hu<PERSON>potId,
        studentTier: StudentTier,
    )

    fun updateCrmTier(
        hubspotIdentifier: HubspotId,
        onboardingTier: OnboardingTier,
    )
}

interface CrmUpdatePaymentTierStatePort {
    fun updateCrmPaymentTierState(
        hubspotIdentifier: HubspotId,
        paymentTierState: PaymentTierState,
    )
}

interface CrmUserCreatePort {
    fun createCrmUser(
        email: String,
        role: UserRole,
        traderReferral: String?,
    ): HubspotId
}

interface CrmUserUpdatePort {
    fun updateCrmUser(request: UpdateCrmCustomerRequest): HubspotId

    fun updateCrmUserQuestionnaire(
        hubspotIdentifier: Long,
        questionnaire: Questionnaire,
    )

    fun updateCrmUserReferralApplied(
        hubspotIdentifier: Long,
        referralApplied: Boolean,
    )
}

interface CrmUserEmailUpdatePort {
    fun updateCrmUserEmail(
        hubspotIdentifier: Long,
        email: String,
    )
}

interface CrmUserPremiumDiscordAccessUpdatePort {
    fun updateCrmUserPremiumDiscordAccess(
        hubspotIdentifier: Long,
        premiumDiscordAccessEnds: Instant,
    )
}

interface CrmUserMentoringWithMentorUpdatePort {
    fun updateCrmUserMentoring(
        hubspotIdentifier: Long,
        mentorPropertyName: String,
        newSessionLeft: Int,
        allSessionsCount: Int,
    )
}

interface CrmCreateCustomerPropertyPort {
    fun createCustomerTextProperty(
        propertyName: String,
        propertyDisplayedLabel: String,
    )

    fun createCustomerCheckboxProperty(
        propertyName: String,
        propertyDisplayedLabel: String,
    )
}

interface CrmUserDiscordUpdatePort {
    fun updateCrmUserDiscordNicknames(
        hubspotIdentifier: HubspotId,
        discordUserName: String?,
        discordGlobalName: String?,
    )
}

interface CrmUserPrivacyUpdatePort {
    fun updateCrmUserPrivacySettings(
        hubspotIdentifier: Long,
        networkingVisibility: NetworkingVisibility?,
        levelVisibility: LevelVisibility?,
    )
}

interface CrmUserLocationUpdatePort {
    fun updateCrmUserLocation(
        hubspotIdentifier: Long,
        realLocation: String?,
    )
}

interface CrmUserGameLevelUpdatePort {
    fun updateCrmUserGameLevel(
        hubspotIdentifier: Long,
        gameLevel: GameLevel?,
    )
}

data class UpdateCrmCustomerRequest(
    val hubspotIdentifier: HubspotId,
    val firstName: String,
    val lastName: String,
    val phone: String?,
    val countryCode: Country,
    val biography: String?,
    val questionnaire: Questionnaire?,
    val tier: StudentTier,
    val realLocation: String? = null,
)
