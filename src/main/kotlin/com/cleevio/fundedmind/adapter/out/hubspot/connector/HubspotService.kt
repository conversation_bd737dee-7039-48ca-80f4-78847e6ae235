package com.cleevio.fundedmind.adapter.out.hubspot.connector

import com.cleevio.fundedmind.adapter.out.hubspot.connector.request.HubspotFieldType
import com.cleevio.fundedmind.adapter.out.hubspot.connector.request.HubspotPropertyRequest
import com.cleevio.fundedmind.adapter.out.hubspot.connector.request.HubspotSearchRequest
import com.cleevio.fundedmind.adapter.out.hubspot.connector.response.HubspotContactResponse
import com.cleevio.fundedmind.application.common.type.HubspotId
import com.cleevio.fundedmind.application.module.crm.port.out.CrmCreateCustomerPropertyPort
import com.cleevio.fundedmind.application.module.crm.port.out.CrmUpdatePaymentTierStatePort
import com.cleevio.fundedmind.application.module.crm.port.out.CrmUserCreatePort
import com.cleevio.fundedmind.application.module.crm.port.out.CrmUserDiscordUpdatePort
import com.cleevio.fundedmind.application.module.crm.port.out.CrmUserEmailUpdatePort
import com.cleevio.fundedmind.application.module.crm.port.out.CrmUserGameLevelUpdatePort
import com.cleevio.fundedmind.application.module.crm.port.out.CrmUserLocationUpdatePort
import com.cleevio.fundedmind.application.module.crm.port.out.CrmUserMentoringWithMentorUpdatePort
import com.cleevio.fundedmind.application.module.crm.port.out.CrmUserPremiumDiscordAccessUpdatePort
import com.cleevio.fundedmind.application.module.crm.port.out.CrmUserPrivacyUpdatePort
import com.cleevio.fundedmind.application.module.crm.port.out.CrmUserRoleUpdatePort
import com.cleevio.fundedmind.application.module.crm.port.out.CrmUserTierUpdatePort
import com.cleevio.fundedmind.application.module.crm.port.out.CrmUserUpdatePort
import com.cleevio.fundedmind.application.module.crm.port.out.UpdateCrmCustomerRequest
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.domain.common.constant.LevelVisibility
import com.cleevio.fundedmind.domain.common.constant.NetworkingVisibility
import com.cleevio.fundedmind.domain.common.constant.OnboardingTier
import com.cleevio.fundedmind.domain.common.constant.PaymentTierState
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.user.Questionnaire
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.infrastructure.config.logger
import org.springframework.stereotype.Service
import java.time.Instant

@Service
class HubspotService(
    private val hubspotContactConnector: HubspotContactConnector,
    private val hubspotPropertiesConnector: HubspotPropertiesConnector,
) : CrmUserCreatePort,
    CrmUserUpdatePort,
    CrmUserTierUpdatePort,
    CrmUpdatePaymentTierStatePort,
    CrmUserRoleUpdatePort,
    CrmUserEmailUpdatePort,
    CrmCreateCustomerPropertyPort,
    CrmUserPremiumDiscordAccessUpdatePort,
    CrmUserMentoringWithMentorUpdatePort,
    CrmUserDiscordUpdatePort,
    CrmUserPrivacyUpdatePort,
    CrmUserLocationUpdatePort,
    CrmUserGameLevelUpdatePort {

    private val logger = logger()

    override fun createCrmUser(
        email: String,
        role: UserRole,
        traderReferral: String?,
    ): HubspotId {
        val hubspotUsers = getCrmUserByEmail(email)
        return if (hubspotUsers.isEmpty()) {
            hubspotContactConnector.create(
                HubspotContactProperties.ofNewContact(
                    email = email,
                    role = role,
                    traderReferral = traderReferral,
                ),
            ).hubspotIdentifier
        } else {
            hubspotUsers.first().hubspotIdentifier
        }
    }

    override fun updateCrmUser(request: UpdateCrmCustomerRequest): HubspotId = hubspotContactConnector.update(
        hubspotIdentifier = request.hubspotIdentifier,
        hubspotContactProp = request.toHubspotContactProperties(),
    ).hubspotIdentifier

    override fun updateCrmUserQuestionnaire(
        hubspotIdentifier: HubspotId,
        questionnaire: Questionnaire,
    ) {
        hubspotContactConnector.update(
            hubspotIdentifier = hubspotIdentifier,
            hubspotContactPropJson = questionnaire.crmProperties(),
        )
    }

    override fun updateCrmTier(
        hubspotIdentifier: HubspotId,
        studentTier: StudentTier,
    ) {
        hubspotContactConnector.update(
            hubspotIdentifier = hubspotIdentifier,
            hubspotContactProp = HubspotContactProperties.ofUpdateStudentTier(
                tier = studentTier,
            ),
        )
    }

    override fun updateCrmTier(
        hubspotIdentifier: HubspotId,
        onboardingTier: OnboardingTier,
    ) {
        hubspotContactConnector.update(
            hubspotIdentifier = hubspotIdentifier,
            hubspotContactProp = HubspotContactProperties.ofUpdateOnboardingTier(
                tier = onboardingTier,
            ),
        )
    }

    override fun updateCrmPaymentTierState(
        hubspotIdentifier: HubspotId,
        paymentTierState: PaymentTierState,
    ) {
        hubspotContactConnector.update(
            hubspotIdentifier = hubspotIdentifier,
            hubspotContactProp = HubspotContactProperties.ofUpdatePaymentTierState(
                paymentTierState = paymentTierState,
            ),
        )
    }

    override fun updateCrmUserRole(
        hubspotIdentifier: HubspotId,
        role: UserRole,
    ) {
        hubspotContactConnector.update(
            hubspotIdentifier = hubspotIdentifier,
            hubspotContactProp = HubspotContactProperties.ofUpdateRole(
                role = role,
            ),
        )
    }

    override fun updateCrmUserReferralApplied(
        hubspotIdentifier: HubspotId,
        referralApplied: Boolean,
    ) {
        hubspotContactConnector.update(
            hubspotIdentifier = hubspotIdentifier,
            hubspotContactProp = HubspotContactProperties.ofUpdateReferralApplied(
                referralApplied = referralApplied,
            ),
        )
    }

    override fun updateCrmUserEmail(
        hubspotIdentifier: HubspotId,
        email: String,
    ) {
        hubspotContactConnector.update(
            hubspotIdentifier = hubspotIdentifier,
            hubspotContactProp = HubspotContactProperties.ofUpdateEmail(
                email = email,
            ),
        )
    }

    override fun updateCrmUserMentoring(
        hubspotIdentifier: HubspotId,
        mentorPropertyName: String,
        newSessionLeft: Int,
        allSessionsCount: Int,
    ) {
        logger.debug("Mentoring $mentorPropertyName updated for hubspot user $hubspotIdentifier.")

        val mentoringSessions = "$newSessionLeft/$allSessionsCount"
        val mentorPropertyMap = mapOf(mentorPropertyName to mentoringSessions)
        hubspotContactConnector.update(
            hubspotIdentifier = hubspotIdentifier,
            hubspotContactPropJson = mentorPropertyMap,
        )
    }

    override fun updateCrmUserPremiumDiscordAccess(
        hubspotIdentifier: HubspotId,
        premiumDiscordAccessEnds: Instant,
    ) {
        logger.debug("Premium discord updated for hubspot user $hubspotIdentifier.")
        hubspotContactConnector.update(
            hubspotIdentifier = hubspotIdentifier,
            HubspotContactProperties.ofUpdatePremiumDiscordAccess(
                premiumDiscordAccessEnds = premiumDiscordAccessEnds,
            ),
        )
    }

    override fun createCustomerTextProperty(
        propertyName: String,
        propertyDisplayedLabel: String,
    ) {
        val request = HubspotPropertyRequest(
            name = propertyName,
            label = propertyDisplayedLabel,
            type = HubspotFieldType.STRING.type,
            fieldType = HubspotFieldType.STRING.fieldType,
        )
        hubspotPropertiesConnector.createCustomerProperty(request)
    }

    override fun createCustomerCheckboxProperty(
        propertyName: String,
        propertyDisplayedLabel: String,
    ) {
        val request = HubspotPropertyRequest(
            name = propertyName,
            label = propertyDisplayedLabel,
            type = HubspotFieldType.CHECKBOX.type,
            fieldType = HubspotFieldType.CHECKBOX.fieldType,
            options = HubspotPropertyRequest.booleanOptions,
        )
        hubspotPropertiesConnector.createCustomerProperty(request)
    }

    override fun updateCrmUserDiscordNicknames(
        hubspotIdentifier: HubspotId,
        discordUserName: String?,
        discordGlobalName: String?,
    ) {
        if (discordUserName == null && discordGlobalName == null) return

        hubspotContactConnector.update(
            hubspotIdentifier = hubspotIdentifier,
            hubspotContactProp = HubspotContactProperties.ofUpdateDiscordNicknames(
                discordUserName = discordUserName,
                discordGlobalName = discordGlobalName,
            ),
        )
    }

    override fun updateCrmUserPrivacySettings(
        hubspotIdentifier: HubspotId,
        networkingVisibility: NetworkingVisibility?,
        levelVisibility: LevelVisibility?,
    ) {
        hubspotContactConnector.update(
            hubspotIdentifier = hubspotIdentifier,
            hubspotContactProp = HubspotContactProperties.ofUpdatePrivacySettings(
                networkingVisibility = networkingVisibility,
                levelVisibility = levelVisibility,
            ),
        )
    }

    override fun updateCrmUserLocation(
        hubspotIdentifier: HubspotId,
        realLocation: String?,
    ) {
        hubspotContactConnector.update(
            hubspotIdentifier = hubspotIdentifier,
            hubspotContactProp = HubspotContactProperties.ofUpdateLocation(
                realLocation = realLocation,
            ),
        )
    }

    override fun updateCrmUserGameLevel(
        hubspotIdentifier: HubspotId,
        gameLevel: GameLevel?,
    ) {
        hubspotContactConnector.update(
            hubspotIdentifier = hubspotIdentifier,
            hubspotContactProp = HubspotContactProperties.ofUpdateGameLevel(
                gameLevel = gameLevel,
            ),
        )
    }

    private fun getCrmUserByEmail(email: String): List<HubspotContactResponse> {
        val response = hubspotContactConnector.getByEmail(
            HubspotSearchRequest(
                filterGroups = listOf(
                    HubspotSearchRequest.FilterGroup(
                        filters = listOf(
                            HubspotSearchRequest.Filter(
                                propertyName = "email",
                                operator = "EQ",
                                value = email,
                            ),
                        ),
                    ),
                ),
            ),
        )
        return response.results
    }
}

private fun UpdateCrmCustomerRequest.toHubspotContactProperties() = HubspotContactProperties.ofUpdateContact(
    firstName = firstName,
    lastName = lastName,
    phone = phone,
    countryCode = countryCode,
    biography = biography,
    questionnaire = questionnaire,
    studentTier = tier,
    realLocation = realLocation,
)
