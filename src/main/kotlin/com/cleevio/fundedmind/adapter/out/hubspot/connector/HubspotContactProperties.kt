package com.cleevio.fundedmind.adapter.out.hubspot.connector

import com.cleevio.fundedmind.domain.common.constant.Country
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.domain.common.constant.LevelVisibility
import com.cleevio.fundedmind.domain.common.constant.NetworkingVisibility
import com.cleevio.fundedmind.domain.common.constant.OnboardingTier
import com.cleevio.fundedmind.domain.common.constant.PaymentTierState
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.user.Questionnaire
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty
import java.time.Instant

@JsonIgnoreProperties(ignoreUnknown = true)
class HubspotContactProperties private constructor(
    @JsonProperty("email") val email: String? = null,
    @JsonProperty("firstname") val firstName: String? = null,
    @JsonProperty("lastname") val lastName: String? = null,
    @JsonProperty("phone") val phone: String? = null,
    @JsonProperty("hs_country_region_code") val countryCode: String? = null,
    @JsonProperty("biography") val biography: String? = null,
    @JsonProperty("questionnaire") val questionnaire: String? = null,
    @JsonProperty("role") val role: UserRole? = null,
    @JsonProperty("tier") val tier: HubspotTier? = null,
    @JsonProperty("premium_discord_access") val premiumDiscordAccess: Instant? = null,
    @JsonProperty("upgrade_tier_state") val paymentTierState: PaymentTierState? = null,
    @JsonProperty("trader_referral") val traderReferral: String? = null,
    @JsonProperty("referral_applied") val referralApplied: Boolean? = null,
    @JsonProperty("discord_user_name") val discordUserName: String? = null,
    @JsonProperty("discord_global_name") val discordGlobalName: String? = null,

    @JsonProperty("networking_visibility") val networkingVisibility: NetworkingVisibility? = null,
    @JsonProperty("level_visibility") val levelVisibility: LevelVisibility? = null,
    @JsonProperty("game_level") val gameLevel: GameLevel? = null,
    @JsonProperty("real_location") val realLocation: String? = null,
) {

    companion object {
        fun ofNewContact(
            email: String,
            role: UserRole,
            traderReferral: String?,
        ) = HubspotContactProperties(
            email = email,
            tier = HubspotTier.NO_TIER,
            role = role,
            traderReferral = traderReferral,
        )

        fun ofUpdateReferralApplied(referralApplied: Boolean) = HubspotContactProperties(
            referralApplied = referralApplied,
        )

        fun ofUpdateContact(
            firstName: String,
            lastName: String,
            phone: String?,
            countryCode: Country,
            biography: String?,
            questionnaire: Questionnaire?,
            studentTier: StudentTier?,
            realLocation: String? = null,
        ) = HubspotContactProperties(
            firstName = firstName,
            lastName = lastName,
            phone = phone,
            countryCode = countryCode.toString(),
            biography = biography,
            questionnaire = questionnaire?.format(),
            tier = studentTier?.let { HubspotTier.fromStudentTier(it) },
            realLocation = realLocation,
        )

        fun ofUpdateStudentTier(tier: StudentTier) = HubspotContactProperties(
            tier = HubspotTier.fromStudentTier(tier),
        )

        fun ofUpdateEmail(email: String) = HubspotContactProperties(
            email = email,
        )

        fun ofUpdateOnboardingTier(tier: OnboardingTier) = HubspotContactProperties(
            tier = HubspotTier.fromOnboardingTier(tier),
        )

        fun ofUpdatePaymentTierState(paymentTierState: PaymentTierState) = HubspotContactProperties(
            paymentTierState = paymentTierState,
        )

        fun ofUpdateRole(role: UserRole) = HubspotContactProperties(
            role = role,
        )

        fun ofUpdatePremiumDiscordAccess(premiumDiscordAccessEnds: Instant) = HubspotContactProperties(
            premiumDiscordAccess = premiumDiscordAccessEnds,
        )

        fun ofUpdateDiscordNicknames(
            discordUserName: String?,
            discordGlobalName: String?,
        ) = HubspotContactProperties(
            discordUserName = discordUserName,
            discordGlobalName = discordGlobalName,
        )

        fun ofUpdatePrivacySettings(
            networkingVisibility: NetworkingVisibility?,
            levelVisibility: LevelVisibility?,
        ) = HubspotContactProperties(
            networkingVisibility = networkingVisibility,
            levelVisibility = levelVisibility,
        )

        fun ofUpdateLocation(realLocation: String?) = HubspotContactProperties(
            realLocation = realLocation,
        )

        fun ofUpdateGameLevel(gameLevel: GameLevel?) = HubspotContactProperties(
            gameLevel = gameLevel,
        )
    }
}
