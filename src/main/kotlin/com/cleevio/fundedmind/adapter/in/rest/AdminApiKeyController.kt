package com.cleevio.fundedmind.adapter.`in`.rest

import com.cleevio.fundedmind.adapter.`in`.ApiVersion
import com.cleevio.fundedmind.adapter.`in`.SwaggerNoAuth
import com.cleevio.fundedmind.adapter.`in`.rest.request.CreateAdminAccountRequest
import com.cleevio.fundedmind.adapter.out.hubspot.connector.request.HubspotFieldType
import com.cleevio.fundedmind.application.common.command.CommandBus
import com.cleevio.fundedmind.application.common.command.IdResult
import com.cleevio.fundedmind.application.common.query.QueryBus
import com.cleevio.fundedmind.application.module.admin.script.command.MigrateDiscordNicknamesCommand
import com.cleevio.fundedmind.application.module.admin.script.command.MigrateStudentPropertiesToHubspotCommand
import com.cleevio.fundedmind.application.module.crm.command.CreateHubspotPropertyCommand
import com.cleevio.fundedmind.application.module.email.command.SendTestEmailCommand
import com.cleevio.fundedmind.application.module.payment.command.GeneratePromotionCodesCommand
import com.cleevio.fundedmind.application.module.payment.query.GetPromotionCodesQuery
import com.cleevio.fundedmind.application.module.user.appuser.command.SyncStripeCustomersForUsersCommand
import com.cleevio.fundedmind.application.module.user.onboarding.command.FinishOnboardingCommand
import com.cleevio.fundedmind.application.module.user.student.command.CreateTestStudentsWithLocationCommand
import com.cleevio.fundedmind.application.module.user.student.command.DeleteTestStudentsWithLocationCommand
import com.cleevio.fundedmind.infrastructure.exception.ExtendedErrorReasonType
import com.cleevio.fundedmind.infrastructure.security.apikey.API_KEY_HEADER_STRING
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestHeader
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.ResponseStatus
import org.springframework.web.bind.annotation.RestController
import java.util.UUID

@Tag(name = "Admin account")
@RestController
@SwaggerNoAuth
@RequestMapping("/admin")
class AdminApiKeyController(
    private val commandBus: CommandBus,
    private val queryBus: QueryBus,
) {

    @Operation(
        description = """
            Finishes onboarding process for a user.
        """,
    )
    @PostMapping("/script/finish-onboarding/{userId}", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.OK)
    fun finishOnboardingForUser(
        @PathVariable userId: UUID,
        @RequestHeader(API_KEY_HEADER_STRING) apiKey: String,
    ): IdResult = commandBus(FinishOnboardingCommand(userId))

    @Operation(
        description = """
            Creates admin account. Account will be automatically verified therefore no email verification is required.
            Authentication is done via API key header 'X-API-KEY' instead of JWT Bearer token.
            400 - Password length must be at least 6 characters.
        """,
    )
    @PostMapping("/account", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.CREATED)
    fun createAdminAccount(
        @RequestBody request: CreateAdminAccountRequest,
        // declare header so swagger can generate a header input field, however auth is done in FundedMindApiKeyFilter
        @RequestHeader(API_KEY_HEADER_STRING) apiKey: String,
    ): IdResult = commandBus(request.toCommand())

    @Operation(
        description = """
            Creates a Stripe Customer for every user that does not yet have one.
        """,
    )
    @PostMapping("/script/sync-stripe-customers", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun syncStripeCustomersForUsers(@RequestHeader(API_KEY_HEADER_STRING) apiKey: String): Unit =
        commandBus(SyncStripeCustomersForUsersCommand(apiKey))

    @Operation(
        description = """
            Sends test email to verify email configuration
        """,
    )
    @PostMapping("/script/test-email/{emailTo}", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun sendTestEmail(
        @RequestHeader(API_KEY_HEADER_STRING) apiKey: String,
        @PathVariable emailTo: String,
    ) {
        commandBus(SendTestEmailCommand(emailTo = emailTo))
    }

    @Operation(
        description = """
            Lists all error reasons. OpenAPI will generate schema for FE that can use it to map errors to messages.
        """,
    )
    @GetMapping("/error-reasons", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.OK)
    fun listErrorReasons(@RequestHeader(API_KEY_HEADER_STRING) apiKey: String): List<ExtendedErrorReasonType> =
        ExtendedErrorReasonType.entries

    @Operation(
        description = """
            Generates promotion codes for the given coupon.
            Returns a list of generated promotion codes.
        """,
    )
    @PostMapping("/script/promotion-codes/{coupon}", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.OK)
    fun generatePromotionCodes(
        @RequestHeader(API_KEY_HEADER_STRING) apiKey: String,
        @PathVariable coupon: String,
        @RequestParam quantity: Int = 10,
        @RequestParam maxRedemptions: Int = 1,
    ): GeneratePromotionCodesCommand.Result =
        commandBus(GeneratePromotionCodesCommand(coupon = coupon, quantity = quantity, maxRedemptions = maxRedemptions))

    @Operation(
        description = """
            Retrieve promotion codes for the given coupon.
        """,
    )
    @GetMapping("/script/promotion-codes/{coupon}", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.OK)
    fun getPromotionCodes(
        @RequestHeader(API_KEY_HEADER_STRING) apiKey: String,
        @PathVariable coupon: String,
    ): GetPromotionCodesQuery.Result = queryBus(GetPromotionCodesQuery(coupon = coupon))

    @Operation(
        description = """
            Creates test students with location data.
            Each student will have a location and discord subscription.
            All test students will have biography: <TEST_MIGRATION>
        """,
    )
    @PostMapping("/script/test-students-with-location/{quantity}", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun createTestStudentsWithLocation(
        @RequestHeader(API_KEY_HEADER_STRING) apiKey: String,
        @PathVariable quantity: Int = 1,
        @RequestParam minRadius: Double = 10_000.0, // 10km
        @RequestParam maxRadius: Double = 75_000.0, // 75km
    ): Unit = commandBus(
        CreateTestStudentsWithLocationCommand(
            quantity = quantity,
            minRadius = minRadius,
            maxRadius = maxRadius,
        ),
    )

    @Operation(
        description = """
            Deletes all test students with location data.
            Performs hard deletion of all students with biography: <TEST_MIGRATION> and their related records.
        """,
    )
    @DeleteMapping("/test-students-with-location", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.OK)
    fun deleteTestStudentsWithLocation(
        @RequestHeader(API_KEY_HEADER_STRING) apiKey: String,
    ): DeleteTestStudentsWithLocationCommand.Result = commandBus(DeleteTestStudentsWithLocationCommand())

    @Operation(
        description = """
            Creates a custom property in Hubspot.
        """,
    )
    @PostMapping("/script/hubspot-property", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun createHubspotProperty(
        @RequestHeader(API_KEY_HEADER_STRING) apiKey: String,
        @RequestParam propertyName: String,
        @RequestParam propertyDisplayedLabel: String,
        @RequestParam type: HubspotFieldType,
    ): Unit = commandBus(
        CreateHubspotPropertyCommand(
            propertyName = propertyName,
            propertyDisplayedLabel = propertyDisplayedLabel,
            type = type,
        ),
    )

    @Operation(
        description = """
            Migrates Discord nicknames from StudentDiscord records to HubSpot custom properties.
            This script fetches all non-deleted StudentDiscord records and updates the corresponding
            HubSpot contacts with Discord username and global name properties.
        """,
    )
    @PostMapping("/script/migrate-discord-nicknames", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun migrateDiscordNicknames(@RequestHeader(API_KEY_HEADER_STRING) apiKey: String): Unit =
        commandBus(MigrateDiscordNicknamesCommand(apiKey = apiKey))

    @Operation(
        description = """
            Migrates student properties (networking visibility, level visibility, game level, and real location)
            from Student records to HubSpot custom properties.
            This script fetches all non-deleted Student records and updates the corresponding
            HubSpot contacts with the current values of these properties.
        """,
    )
    @PostMapping("/script/migrate-student-properties-to-hubspot", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun migrateStudentPropertiesToHubspot(@RequestHeader(API_KEY_HEADER_STRING) apiKey: String): Unit =
        commandBus(MigrateStudentPropertiesToHubspotCommand(apiKey = apiKey))
}
